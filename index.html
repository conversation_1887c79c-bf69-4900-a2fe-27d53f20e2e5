<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> - Full Stack Developer Portfolio</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap"
        rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- EmailJS -->
    <script type="text/javascript" src="https://cdn.jsdelivr.net/npm/@emailjs/browser@4/dist/email.min.js"></script>
</head>

<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="hamburger">
                <span></span>
                <span></span>
                <span></span>
            </div>
            <div class="nav-logo"><PERSON><PERSON><PERSON></div>
            <ul class="nav-menu">
                <li><a href="#home" class="nav-link">Home</a></li>
                <li><a href="#about" class="nav-link">About Me</a></li>
                <li><a href="#work" class="nav-link">Portfolio</a></li>
                <li><a href="#contact" class="nav-link">Contact</a></li>
                <div class="nav-buttons">
                    <a href="./Resume.pdf" download="Resume.pdf" class="nav-btn btn-secondary">Resume</a>
                    <a href="#contact" class="nav-btn btn-primary">Connect</a>
                </div>
            </ul>
            <div class="nav-buttons-desktop">
                <a href="./Resume.pdf" download="Resume.pdf" class="nav-btn btn-secondary">Resume</a>
                <a href="#contact" class="nav-btn btn-primary">Connect</a>
            </div>
        </div>
    </nav>

    <!-- Home Section -->
    <section id="home" class="hero">
        <div class="hero-background">
            <div class="floating-shapes">
                <div class="shape shape-1"></div>
                <div class="shape shape-2"></div>
                <div class="shape shape-3"></div>
                <div class="shape shape-4"></div>
                <div class="shape shape-5"></div>
                <div class="shape shape-6"></div>
                <div class="shape shape-7"></div>
                <div class="shape shape-8"></div>
            </div>
        </div>
        <div class="hero-content">
            <div class="hero-text">
                <h1 class="hero-title">
                    I'm <span class="gradient-text">Balkaran Singh</span>, <br>
                    Full Stack Developer & Competitive Programmer based in Punjab, India.
                </h1>
                <div class="tech-stack-subtitle">
                    <span class="typing-text"></span>
                </div>
                <p class="hero-subtitle">I build scalable web apps and solve complex algorithmic problems. From crafting
                    seamless user experiences to competing in high-rated coding contests—tech is my playground.</p>
            </div>
        </div>
        <div class="scroll-indicator">
            <div class="scroll-mouse">
                <div class="scroll-wheel"></div>
            </div>
            <span class="scroll-text">Scroll to explore</span>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="about">
        <div class="about-background">
            <div class="floating-shapes">
                <div class="shape shape-1"></div>
                <div class="shape shape-2"></div>
                <div class="shape shape-3"></div>
                <div class="shape shape-4"></div>
                <div class="shape shape-5"></div>
                <div class="shape shape-6"></div>
                <div class="shape shape-7"></div>
                <div class="shape shape-8"></div>
            </div>
        </div>
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">About Me</h2>
                <p class="section-subtitle">Passionate about building innovative websites and solving algorithmic
                    challenges.
                </p>
            </div>
            <div class="about-content">
                <div class="about-text">
                    <p>
                        I'm a creative and curious full stack developer with hands-on experience in building responsive,
                        scalable web applications. I specialize in modern technologies like <strong>React, Node.js,
                            MongoDB, Express, Tailwind CSS</strong>, and <strong>Cloudinary</strong>. Alongside
                        development, I'm passionate about <strong>Competitive Programming</strong> — having solved
                        <a href="https://leetcode.com/u/Balkaran786/" target="_blank">800+ problems on LeetCode with 1850+ contest Rating</a>,
                        <a href="https://www.geeksforgeeks.org/user/balkara3a6e/" target="_blank">100+ on GFG</a>, and
                        earned
                        <a href="https://www.naukri.com/code360/profile/be3ea7c5-ada2-4752-af60-cdfd2fd236ab" target="_blank">30+ badges
                            on Coding Ninjas</a> across core DSA topics.
                        I've also completed in-depth coursework in <strong>Data Structures and Algorithms, DBMS,
                            Computer Networks</strong>, and <strong>OOPs</strong>. I love building full-stack
                        projects and optimizing code for speed and memory while constantly pushing my learning
                        boundaries.
                    </p>

                </div>
            </div>
        </div>
    </section>

    <!-- Work Section -->
    <section id="work" class="work">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">My Work</h2>
                <p class="section-subtitle">A showcase of my recent projects</p>
            </div>
            <!-- Mobile instruction box -->
            <div class="mobile-instruction-box">
                <i class="fas fa-hand-pointer"></i>
                <p>Click on the images to know the details about my projects</p>
            </div>
            <div class="work-grid">
                <div class="work-item">
                    <div class="work-image">
                        <img src="Fluento.png" alt="Fluento Language Learning App">
                        <div class="work-overlay">
                            <div class="work-info">
                                <h3>Fluento - Connect & Speak</h3>
                                <p>Modern language learning chat app with React & Node.js</p>
                                <div class="work-links">
                                    <a href="https://fluento-project.onrender.com/" target="_blank" class="work-link"><i class="fas fa-external-link-alt"></i></a>
                                    <a href="https://github.com/Balkaran786-dev/Fluento-Project" target="_blank" class="work-link"><i class="fab fa-github"></i></a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="work-item">
                    <div class="work-image">
                        <img src="URL.png" alt="Shrinkster URL Shortener">
                        <div class="work-overlay">
                            <div class="work-info">
                                <h3>Shrinkster - A Next-Gen URL Shortner</h3>
                                <p>Simply URLs,Amplify Reach</p>
                                <div class="work-links">
                                    <a href="https://shrinkster-shorten-your-urls.onrender.com" target="_blank" class="work-link"><i class="fas fa-external-link-alt"></i></a>
                                    <a href="https://github.com/Balkaran786-dev/Shrinkster-Shorten-your-URLs" target="_blank" class="work-link"><i class="fab fa-github"></i></a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="work-item">
                    <div class="work-image">
                        <img src="Dijkstra.png" alt="Dijkstagram Path Visualizer">
                        <div class="work-overlay">
                            <div class="work-info">
                                <h3>Dijkstagram — Visualize Paths,Code Logic</h3>
                                <p>Interactive Dijkstra Visualizer with Code Editor</p>
                                <div class="work-links">
                                    <a href="https://dijkstra-visualizer-13.onrender.com/" target="_blank" class="work-link"><i class="fas fa-external-link-alt"></i></a>
                                    <a href="https://github.com/Balkaran786-dev/Dijkstra-Visualizer" target="_blank" class="work-link"><i class="fab fa-github"></i></a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>



    <!-- Contact Section -->
    <section id="contact" class="contact">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Get In Touch</h2>
                <p class="section-subtitle">Let's work together on your next project</p>
            </div>
            <div class="contact-content">
                <div class="contact-info">
                    <div class="contact-item">
                        <i class="fas fa-envelope"></i>
                        <div>
                            <h4>Email</h4>
                            <p><EMAIL></p>
                        </div>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-phone"></i>
                        <div>
                            <h4>Phone</h4>
                            <p>+91-6239534216</p>
                        </div>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <div>
                            <h4>Location</h4>
                            <p>Punjab, India</p>
                        </div>
                    </div>
                </div>
                <form class="contact-form" id="contact-form">
                    <div class="form-group">
                        <input type="text" id="user_name" name="user_name" placeholder="Your Name" required>
                    </div>
                    <div class="form-group">
                        <input type="email" id="user_email" name="user_email" placeholder="Your Email" required>
                    </div>
                    <div class="form-group">
                        <textarea id="message" name="message" placeholder="Your Message" rows="5" required></textarea>
                    </div>
                    <button type="submit" class="btn btn-primary">Send Message</button>
                    <div id="status-message" style="margin-top: 10px; padding: 10px; display: none;"></div>
                </form>
            </div>
        </div>
    </section>

    <script>
        // Initialize EmailJS with your public key
        emailjs.init("tYLB5jNhwFzzP_pW_");

        // Handle form submission
        document.getElementById('contact-form').addEventListener('submit', function(event) {
            event.preventDefault();

            const statusMessage = document.getElementById('status-message');
            const submitButton = event.target.querySelector('button[type="submit"]');

            // Show loading state
            submitButton.textContent = 'Sending...';
            submitButton.disabled = true;
            statusMessage.style.display = 'block';
            statusMessage.style.backgroundColor = '#e3f2fd';
            statusMessage.style.color = '#1976d2';
            statusMessage.textContent = 'Sending your message...';

            // Send email using EmailJS
            emailjs.sendForm('service_3ukt8ur', 'template_sf2g35m', this)
                .then(function() {
                    // Success
                    statusMessage.style.backgroundColor = '#e8f5e8';
                    statusMessage.style.color = '#2e7d32';
                    statusMessage.style.border = '1px solid #4caf50';
                    statusMessage.style.borderRadius = '5px';
                    statusMessage.style.padding = '10px';
                    statusMessage.textContent = '✅ Message sent successfully! I\'ll get back to you soon.';

                    // Reset form
                    document.getElementById('contact-form').reset();

                    // Hide success message after 5 seconds
                    setTimeout(function() {
                        statusMessage.style.display = 'none';
                    }, 5000);

                }, function(error) {
                    // Error
                    statusMessage.style.backgroundColor = '#ffebee';
                    statusMessage.style.color = '#c62828';
                    statusMessage.style.border = '1px solid #f44336';
                    statusMessage.style.borderRadius = '5px';
                    statusMessage.style.padding = '10px';
                    statusMessage.textContent = '❌ Failed to send message. Please try again or email me directly.';
                    console.log('EmailJS error:', error);
                })
                .finally(function() {
                    // Reset button
                    submitButton.textContent = 'Send Message';
                    submitButton.disabled = false;
                });
        });
    </script>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <p>&copy; 2025 Balkaran. All rights reserved.</p>
                <div class="social-links">
                    <a href="https://linkedin.com/in/balkaran-singh-5b09532a9" target="_blank"><i class="fab fa-linkedin"></i></a>
                    <a href="https://github.com/Balkaran786-dev" target="_blank"><i class="fab fa-github"></i></a>
                </div>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
</body>

</html>