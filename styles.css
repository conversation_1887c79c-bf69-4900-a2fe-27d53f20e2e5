* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #8b5cf6;
    --secondary-color: #ec4899;
    --accent-color: #f59e0b;
    --text-primary: #ffffff;
    --text-secondary: #d1d5db;
    --text-muted: #9ca3af;
    --bg-primary: #0f0f0f;
    --bg-secondary: #1a1a1a;
    --bg-dark: #000000;
    --gradient: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    --gradient-orange: linear-gradient(135deg, #f59e0b, #ec4899);
    --shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 20px 40px rgba(0, 0, 0, 0.4);
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--bg-dark);
    overflow-x: hidden;
    opacity: 0;
    transition: opacity 0.3s ease-in-out;
    width: 100%;
    max-width: 100vw;
}

body.loaded {
    opacity: 1;
}

html {
    scroll-behavior: smooth;
}

/* Glow animation for buttons */
@keyframes glow-pulse {
    0% {
        box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4), 0 0 20px rgba(245, 158, 11, 0.6), 0 0 40px rgba(245, 158, 11, 0.3);
    }
    100% {
        box-shadow: 0 8px 25px rgba(245, 158, 11, 0.6), 0 0 25px rgba(245, 158, 11, 0.8), 0 0 50px rgba(245, 158, 11, 0.5);
    }
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: var(--bg-dark);
    z-index: 1000;
    padding: 1rem 0;
    transition: none;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo {
    font-size: 1.5rem;
    font-weight: 700;
    background: var(--gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: var(--primary-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient);
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

.nav-buttons {
    display: none;
}

.nav-buttons-desktop {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.nav-btn {
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.nav-btn.btn-primary {
    background: var(--gradient-orange);
    color: white;
    box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
}

.nav-btn.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4), 0 0 20px rgba(245, 158, 11, 0.6), 0 0 40px rgba(245, 158, 11, 0.3);
    animation: glow-pulse 2s ease-in-out infinite alternate;
}

.nav-btn.btn-secondary {
    background: transparent;
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.nav-btn.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
    border-color: rgba(255, 255, 255, 0.5);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

/* Desktop/Laptop styles */
@media (min-width: 769px) {
    .nav-buttons-desktop {
        display: flex;
    }

    .hamburger {
        display: none;
    }
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: var(--text-primary);
    margin: 3px 0;
    transition: 0.3s;
    transform-origin: center;
}

.hamburger.active span:nth-child(1) {
    transform: rotate(45deg) translate(6px, 6px);
}

.hamburger.active span:nth-child(2) {
    opacity: 0;
}

.hamburger.active span:nth-child(3) {
    transform: rotate(-45deg) translate(6px, -6px);
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-dark);
    position: relative;
    text-align: center;
    padding: 6rem 2rem 2rem;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
}

.floating-shapes {
    position: relative;
    width: 100%;
    height: 100%;
}

.shape {
    position: absolute;
    border-radius: 50%;
    animation: float 8s ease-in-out infinite;
    opacity: 0.6;
}

.shape-1 {
    width: 100px;
    height: 100px;
    top: 15%;
    left: 8%;
    background: linear-gradient(135deg, rgba(139, 92, 246, 0.2), rgba(99, 102, 241, 0.1));
    animation-delay: 0s;
}

.shape-2 {
    width: 150px;
    height: 150px;
    top: 65%;
    right: 12%;
    background: linear-gradient(135deg, rgba(236, 72, 153, 0.2), rgba(219, 39, 119, 0.1));
    animation-delay: 2s;
}

.shape-3 {
    width: 80px;
    height: 80px;
    top: 25%;
    right: 30%;
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.2), rgba(251, 191, 36, 0.1));
    animation-delay: 4s;
}

.shape-4 {
    width: 120px;
    height: 120px;
    bottom: 25%;
    left: 15%;
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.2), rgba(52, 211, 153, 0.1));
    animation-delay: 1s;
}

.shape-5 {
    width: 90px;
    height: 90px;
    top: 10%;
    right: 8%;
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(248, 113, 113, 0.1));
    animation-delay: 3s;
}

.shape-6 {
    width: 110px;
    height: 110px;
    bottom: 15%;
    right: 25%;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(96, 165, 250, 0.1));
    animation-delay: 5s;
}

.shape-7 {
    width: 70px;
    height: 70px;
    top: 45%;
    left: 5%;
    background: linear-gradient(135deg, rgba(168, 85, 247, 0.2), rgba(196, 181, 253, 0.1));
    animation-delay: 6s;
}

.shape-8 {
    width: 130px;
    height: 130px;
    top: 50%;
    right: 5%;
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.2), rgba(74, 222, 128, 0.1));
    animation-delay: 7s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.hero-content {
    max-width: 900px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1.5rem;
    color: white;
    text-align: center;
}

.gradient-text {
    background: linear-gradient(45deg, #8b5cf6, #ec4899, #f59e0b, #8b5cf6);
    background-size: 300% 300%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradient-shift 3s ease-in-out infinite;
}

@keyframes gradient-shift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

.tech-stack-subtitle {
    margin: 1rem auto 0.5rem auto;
    text-align: center;
    opacity: 1;
    min-height: 1.5rem;
}

.typing-text {
    font-size: 1rem;
    color: var(--accent-color);
    font-weight: 500;
    position: relative;
    display: inline-block;
}

.typing-text::after {
    content: '|';
    color: var(--accent-color);
    animation: blink-caret 1s step-end infinite;
    position: absolute;
    right: -8px;
}

@keyframes fade-in {
    to {
        opacity: 1;
    }
}

@keyframes blink-caret {
    0%, 50% {
        opacity: 1;
    }
    51%, 100% {
        opacity: 0;
    }
}

.hero-subtitle {
    font-size: 1.1rem;
    color: var(--text-muted);
    margin: 0 auto 2.5rem auto;
    text-align: center;
    max-width: 600px;
    line-height: 1.6;
}

.scroll-indicator {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    z-index: 3;
}

.scroll-mouse {
    width: 24px;
    height: 40px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    position: relative;
    display: flex;
    justify-content: center;
    padding-top: 6px;
}

.scroll-wheel {
    width: 3px;
    height: 8px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 2px;
    animation: scroll-wheel 2s infinite;
}

@keyframes scroll-wheel {
    0% { transform: translateY(0); opacity: 1; }
    100% { transform: translateY(16px); opacity: 0; }
}

.scroll-text {
    font-size: 0.8rem;
    color: var(--text-muted);
    font-weight: 500;
}

.hero-buttons {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

.btn {
    padding: 1rem 2rem;
    border: none;
    border-radius: 50px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 1rem;
    cursor: pointer;
    display: inline-block;
}

.btn-primary {
    background: var(--gradient-orange);
    color: white;
    box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4), 0 0 20px rgba(245, 158, 11, 0.6), 0 0 40px rgba(245, 158, 11, 0.3);
    animation: glow-pulse 2s ease-in-out infinite alternate;
}

.btn-secondary {
    background: transparent;
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
    border-color: rgba(255, 255, 255, 0.5);
}

/* Responsive Design */
@media (max-width: 768px) {
    /* Ensure full width coverage */
    html, body {
        width: 100%;
        max-width: 100vw;
        overflow-x: hidden;
    }

    .nav-container {
        padding: 0 0.8rem;
        justify-content: flex-start;
        gap: 1rem;
        width: 100%;
        max-width: 100vw;
    }

    .nav-logo {
        order: 2;
        flex: 1;
        text-align: right;
        font-size: 1.2rem;
        padding-right: 0.5rem;
    }

    .hamburger {
        display: flex;
        order: 1;
    }

    .nav-buttons-desktop {
        display: none;
    }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 50px;
        flex-direction: column;
        background-color: var(--bg-dark);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: var(--shadow);
        padding: 2rem 0;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        z-index: 1000;
        order: 3;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-menu li {
        margin: 1rem 0;
    }

    .nav-menu .nav-buttons {
        display: flex;
        flex-direction: row;
        gap: 0.8rem;
        margin-top: 1.5rem;
        padding: 0 2rem;
        justify-content: center;
    }

    .nav-btn {
        flex: 1;
        max-width: 120px;
        padding: 0.6rem 0.8rem;
        font-size: 0.75rem;
        text-align: center;
        white-space: nowrap;
    }

    .navbar {
        padding: 0.4rem 0;
        margin: 0;
        width: 100%;
        max-width: 100vw;
    }

    .nav-logo {
        font-size: 1.2rem;
    }

    .hero-title {
        font-size: 2.5rem;
        line-height: 1.2;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .tech-stack-subtitle {
        font-size: 0.9rem;
    }

    .work-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    /* Show mobile instruction box only on mobile */
    .mobile-instruction-box {
        display: block;
    }

    .about-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .skills-grid {
        grid-template-columns: 1fr;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .contact-form {
        width: 100%;
        max-width: 100%;
        padding: 0;
    }

    .form-group input,
    .form-group textarea {
        width: 100%;
        box-sizing: border-box;
        padding: 0.8rem;
        font-size: 0.9rem;
    }

    .contact-info {
        margin-bottom: 1rem;
    }

    .contact-item {
        margin-bottom: 1.5rem;
    }

    .footer-content {
        flex-direction: column;
        gap: 1rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .container {
        padding: 0 1.5rem;
        margin: 0 0.5rem;
    }

    .hero {
        padding: 2rem 1rem 4rem;
        margin: 0 0.5rem;
        width: calc(100% - 1rem);
        max-width: calc(100vw - 1rem);
        overflow: hidden;
    }

    .section {
        padding: 4rem 0;
        margin: 0 0.5rem;
        width: calc(100% - 1rem);
        max-width: calc(100vw - 1rem);
        overflow: hidden;
    }

    .about {
        margin: 0 0.5rem;
        width: calc(100% - 1rem);
        max-width: calc(100vw - 1rem);
        overflow: hidden;
    }

    .floating-shapes .shape {
        opacity: 0.3;
    }

    /* Hide problematic shapes on mobile to prevent overflow */
    .shape-2, .shape-3, .shape-5, .shape-6, .shape-8 {
        display: none;
    }

    /* Adjust remaining shapes to stay within viewport */
    .shape-1 {
        left: 5%;
        width: 60px;
        height: 60px;
    }

    .shape-4 {
        left: 10%;
        width: 80px;
        height: 80px;
    }

    .shape-7 {
        left: 3%;
        width: 50px;
        height: 50px;
    }

    /* Add mobile-specific background elements to fill empty space */
    .hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background:
            radial-gradient(circle at 15% 20%, rgba(139, 92, 246, 0.1) 0%, transparent 40%),
            radial-gradient(circle at 85% 30%, rgba(236, 72, 153, 0.08) 0%, transparent 35%),
            radial-gradient(circle at 50% 60%, rgba(245, 158, 11, 0.06) 0%, transparent 30%),
            radial-gradient(circle at 25% 80%, rgba(16, 185, 129, 0.08) 0%, transparent 35%);
        z-index: 1;
        pointer-events: none;
    }

    /* Add subtle animated dots pattern */
    .hero::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image:
            radial-gradient(circle at 20% 25%, rgba(255, 255, 255, 0.03) 1px, transparent 1px),
            radial-gradient(circle at 70% 15%, rgba(255, 255, 255, 0.02) 1px, transparent 1px),
            radial-gradient(circle at 40% 70%, rgba(255, 255, 255, 0.025) 1px, transparent 1px),
            radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.02) 1px, transparent 1px);
        background-size: 50px 50px, 80px 80px, 60px 60px, 70px 70px;
        z-index: 1;
        pointer-events: none;
        animation: float 20s ease-in-out infinite;
    }
}

/* About Section */
.about {
    padding: 5rem 0;
    background: var(--bg-dark);
    position: relative;
    overflow: hidden;
}

.about-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
}

/* Section Styles */
section {
    padding: 6rem 0;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
    position: relative;
    z-index: 2;
}

.section-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: var(--gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-subtitle {
    font-size: 1.25rem;
    color: var(--text-secondary);
}

.about-content {
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
    position: relative;
    z-index: 2;
}

.about-text p {
    font-size: 1.125rem;
    color: var(--text-secondary);
    margin-bottom: 3rem;
    line-height: 1.8;
}

.about-text a {
    color: #8b5cf6;
    text-decoration: none;
    font-weight: 600;
    background: linear-gradient(135deg, #8b5cf6, #ec4899);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    transition: all 0.3s ease;
    position: relative;
    display: inline-block;
}

.about-text a:hover {
    transform: translateY(-2px);
    text-shadow: 0 4px 8px rgba(139, 92, 246, 0.3);
}

.about-text a::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(135deg, #8b5cf6, #ec4899);
    transition: width 0.3s ease;
}

.about-text a:hover::after {
    width: 100%;
}

.skills {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    max-width: 600px;
    margin: 0 auto;
}

.skill-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.skill-name {
    font-weight: 600;
    color: var(--text-primary);
}

.skill-bar {
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
}

.skill-progress {
    height: 100%;
    background: var(--gradient);
    border-radius: 4px;
    transition: width 1s ease;
    width: 0;
}



/* Work Section */
/* Desktop instruction box */
.desktop-instruction-box {
    background: rgba(30, 30, 30, 0.9);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 1rem 2rem;
    margin: 2rem auto;
    text-align: center;
    max-width: 500px;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.desktop-instruction-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #ffeaa7, #dda0dd, #ff6b6b);
    background-size: 400% 400%;
    border-radius: 12px;
    z-index: -1;
    opacity: 0;
    animation: gradientShift 3s ease infinite;
    transition: opacity 0.3s ease;
    mask:
        linear-gradient(#fff 0 0) content-box,
        linear-gradient(#fff 0 0);
    mask-composite: xor;
    -webkit-mask:
        linear-gradient(#fff 0 0) content-box,
        linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    padding: 2px;
}

.desktop-instruction-box:hover::before {
    opacity: 1;
}

.desktop-instruction-box:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.instruction-text {
    color: #ffffff;
    font-size: 1.1rem;
    font-weight: 500;
    margin: 0 1rem;
}

.arrow-down {
    color: #ff6b6b;
    font-size: 1.5rem;
    font-weight: bold;
    animation: bounceArrow 2s ease-in-out infinite;
    display: inline-block;
}

.arrow-down:last-child {
    animation-delay: 0.1s;
}

@keyframes gradientShift {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

@keyframes bounceArrow {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-8px);
    }
    60% {
        transform: translateY(-4px);
    }
}

/* Mobile instruction box - hidden by default */
.mobile-instruction-box {
    display: none;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 12px;
    text-align: center;
    margin: 1.5rem 0;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.mobile-instruction-box i {
    font-size: 1.2rem;
    margin-right: 0.5rem;
    color: #ffd700;
}

.mobile-instruction-box p {
    margin: 0;
    font-size: 0.95rem;
    font-weight: 500;
    display: inline;
}

.work-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.work-item {
    border-radius: 20px;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: transform 0.3s ease;
}

.work-item:hover {
    transform: translateY(-10px);
}

.work-image {
    position: relative;
    overflow: hidden;
}

.work-image img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.work-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.work-item:hover .work-overlay {
    opacity: 1;
}

.work-item:hover .work-image img {
    transform: scale(1.1);
}

.work-info {
    text-align: center;
    color: white;
    padding: 2rem;
}

.work-info h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.work-info p {
    margin-bottom: 1rem;
    opacity: 0.8;
}

.work-links {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

.work-link {
    width: 40px;
    height: 40px;
    background: var(--gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: transform 0.3s ease;
}

.work-link:hover {
    transform: scale(1.1);
}

/* Services Section */
.services {
    background: var(--bg-secondary);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.service-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    padding: 2.5rem 2rem;
    border-radius: 20px;
    text-align: center;
    backdrop-filter: blur(10px);
    transition: transform 0.3s ease;
}

.service-card:hover {
    transform: translateY(-10px);
    border-color: rgba(139, 92, 246, 0.3);
    box-shadow: 0 20px 40px rgba(139, 92, 246, 0.1);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    color: white;
    font-size: 2rem;
}

.service-card h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.service-card p {
    color: var(--text-secondary);
}

/* Contact Section */
.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.contact-item i {
    width: 50px;
    height: 50px;
    background: var(--gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.contact-item h4 {
    font-size: 1.125rem;
    margin-bottom: 0.25rem;
    color: var(--text-primary);
}

.contact-item p {
    color: var(--text-secondary);
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    font-family: inherit;
    font-size: 1rem;
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-primary);
    transition: border-color 0.3s ease;
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: var(--text-muted);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
}

/* Footer */
.footer {
    background: var(--bg-dark);
    color: white;
    padding: 2rem 0;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: background 0.3s ease;
}

.social-links a:hover {
    background: var(--primary-color);
}



@media (max-width: 480px) {
    /* Ensure full width coverage on very small screens */
    html, body {
        width: 100%;
        max-width: 100vw;
        overflow-x: hidden;
    }

    .hero-title {
        font-size: 2rem;
        line-height: 1.1;
    }

    .hero-subtitle {
        font-size: 0.9rem;
    }

    .tech-stack-subtitle {
        font-size: 0.8rem;
    }

    .typing-text {
        font-size: 0.8rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .btn {
        width: 100%;
        max-width: 250px;
        padding: 0.8rem 1.5rem;
        font-size: 0.9rem;
    }

    .nav-btn {
        padding: 0.6rem 1.2rem;
        font-size: 0.8rem;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .work-item {
        margin-bottom: 1.5rem;
    }

    .contact-form {
        padding: 0;
        width: 100%;
        max-width: 100%;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        padding: 0;
    }

    .form-group input,
    .form-group textarea {
        width: 100%;
        box-sizing: border-box;
        padding: 0.75rem;
        font-size: 0.85rem;
        border-radius: 8px;
    }

    .contact-item {
        margin-bottom: 1rem;
        flex-direction: row;
        align-items: flex-start;
    }

    .contact-item i {
        width: 40px;
        height: 40px;
        font-size: 1rem;
        flex-shrink: 0;
    }

    .contact-item div {
        flex: 1;
    }

    .contact-item h4 {
        font-size: 1rem;
        margin-bottom: 0.25rem;
    }

    .contact-item p {
        font-size: 0.9rem;
        word-break: break-word;
    }

    .hero {
        padding: 1rem 0.5rem 3rem;
        margin: 0 0.75rem;
        width: calc(100% - 1.5rem);
        max-width: calc(100vw - 1.5rem);
    }

    .container {
        padding: 0 1rem;
        margin: 0 0.75rem;
    }

    /* Add margins to all sections for small screens */
    section:not(.navbar) {
        margin: 0 0.75rem;
        width: calc(100% - 1.5rem);
        max-width: calc(100vw - 1.5rem);
    }

    .work-grid {
        grid-template-columns: 1fr;
    }

    .services-grid {
        grid-template-columns: 1fr;
    }
}
